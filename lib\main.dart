

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/routes/app_router.dart';
import 'package:newarc_platform/routes/user_provider.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:provider/provider.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_strategy/url_strategy.dart';
import 'package:web/web.dart' as web;
import 'classes/agencyUser.dart';
import 'classes/professionals.dart';
import 'classes/supplier.dart';
import 'classes/user.dart';


Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if ( !appConfig.isProduction) {
    FlutterError.onError = (FlutterErrorDetails details) {
      if (details.exception.toString().contains('Trying to render a disposed EngineFlutterView')) {
        // Ignore this specific engine error
        return;
      }
      print({'error', details});
      FlutterError.dumpErrorToConsole(details);
    };
  }

  if (appConfig.isProduction) {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey: appConfig.FIREBASE_PRODUCTION_API_KEY,
          appId: appConfig.FIREBASE_PRODUCTION_APP_ID,
          messagingSenderId: appConfig.FIREBASE_PRODUCTION_MESSAGING_SENDER_ID,
          storageBucket: appConfig.FIREBASE_PRODUCTION_STORAGE_BUCKET,
          projectId: appConfig.FIREBASE_PRODUCTION_PROJECT_ID,
        ));
  } else {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey: appConfig.FIREBASE_STAGING_API_KEY,
          appId: appConfig.FIREBASE_STAGING_APP_ID,
          messagingSenderId: appConfig.FIREBASE_STAGING_MESSAGING_SENDER_ID,
          storageBucket: appConfig.FIREBASE_STAGING_STORAGE_BUCKET,
          projectId: appConfig.FIREBASE_STAGING_PROJECT_ID,
        ));
  }


  setPathUrlStrategy();

  // Redirects to login platform based on url, can be among "work", "professionals", "agency"
  final currentRoute = Uri.parse(web.window.location.href);
  String platformName;
  if (currentRoute.host.contains('work.newarc.it')) {
    platformName = 'work';
  } else if (currentRoute.host.contains('pro.newarc.it')) {
    platformName = 'professionals';
  } else if (currentRoute.host.contains('agenzie.newarc.it')) {
    platformName = 'agency';
  } else {
    platformName = 'agency'; // default for staging, to be changed if needed (remeber to change equivalent in login_page.dart)
  }
  debugPrint('platformName: $platformName');
  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (_) => UserProvider()),
    ],
    ///TODO: ------>>>> When deploying to staging, set `accessType` to static for the respective platform being deployed.
    child: MyApp(accessType: platformName),
  ),);
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}

class MyApp extends StatefulWidget {
  final String accessType;
  MyApp({
    this.accessType =  'agency',
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final GoRouter _router;

  @override
  void initState() {
    super.initState();
    _router = AppRouter.createRouter(widget.accessType, appConfig.isProduction);
    _listenAuthState(widget.accessType);
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = context.watch<UserProvider>();
    if (userProvider.isLoading) {
      return  MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
        backgroundColor: widget.accessType == 'work' ? Color(0xff262626) : widget.accessType == 'professionals' ? Colors.black : Color(0xff1c1e21),
        body: Center(
          child: CircularProgressIndicator(
            color: widget.accessType == 'work' ? Color(0xff499B79) : widget.accessType == 'professionals' ? const Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
          ),
        ),
      ));
    }
    ThemeData newarcTheme = setTheme(widget.accessType);
    return MaterialApp.router(
      title: 'Newarc Platform',
      scrollBehavior: MyCustomScrollBehavior(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('it', 'IT')
      ],
      theme: newarcTheme.copyWith(
        textTheme: GoogleFonts.ralewayTextTheme(newarcTheme.textTheme),
      ),
      locale: Locale('it'),
      routerConfig: _router,
      debugShowCheckedModeBanner: false,
    );
  }

  ThemeData setTheme(String accessType) {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
      ),
      highlightColor: Color(0xff5DB1E3),
      primaryColor: accessType == 'work' ? Color(0xff499B79) : accessType == 'professionals' ? const Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
      primaryColorDark: accessType == 'work' ? Color(0xff262626) : accessType == 'professionals' ? Colors.black : Color(0xff1c1e21),
      primaryColorLight: Color(0xff6C6C6C),
      unselectedWidgetColor: Colors.white,
      disabledColor: Colors.black,
      switchTheme: SwitchThemeData(
        trackColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return Color(0xffE5E5E5);
          }
          return Colors.white;
        }),
        thumbColor: WidgetStateProperty.all(Color(0xffC0C0C0)),
        trackOutlineWidth: WidgetStateProperty.all(1),
        trackOutlineColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.transparent;
          }
          return Color(0xffC0C0C0);
        }),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: Color(0xff262626),
          fontSize: 25,
          fontWeight: FontWeight.w800,
        ),
        headlineMedium: TextStyle(
          color: Color(0xff262626),
          fontSize: 23,
          fontWeight: FontWeight.w800,
        ),
        headlineSmall: TextStyle(
          color: Color(0xff262626),
          fontSize: 21,
          fontWeight: FontWeight.w800,
        ),
      ),
      textSelectionTheme: TextSelectionThemeData(
        selectionColor: Color(0xffE4E4E4),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A)),
      dropdownMenuTheme: DropdownMenuThemeData(
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: Colors.white,
          ),
          menuStyle: MenuStyle(
            backgroundColor: WidgetStateProperty.all(Colors.white),
            surfaceTintColor: WidgetStateProperty.all(Colors.white),
          )),
      sliderTheme: SliderThemeData(
        activeTickMarkColor: Colors.transparent,
        inactiveTickMarkColor: Colors.transparent,
        activeTrackColor: Color(0xff5e5e5e), // riga a sinistra
        overlayColor: Colors.transparent,
        overlayShape: RoundSliderOverlayShape(overlayRadius: 25), // gestisce splash radius dell'indicatore
        thumbColor: Color(0xff5e5e5e), // colore indicatore
        thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8), // dimensione dell'indicatore
      ),
      checkboxTheme: CheckboxThemeData(
        overlayColor: WidgetStateProperty.all(Colors.white),
        fillColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
          }
          return Colors.white;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        side: WidgetStateBorderSide.resolveWith(
          (states) => BorderSide(
            width: 1.0,
            color: states.contains(WidgetState.selected) ? Colors.transparent : Color(0xffdbdbdb),
          ),
        ),
      ),
      datePickerTheme: DatePickerThemeData(
          backgroundColor: Colors.white,
          dayOverlayColor: WidgetStateProperty.all(
            Color(0xffE5E5E5),
          ),
          dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
            }
            return Colors.transparent;
          }),
          todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
            }
            return Colors.white;
          }),
          todayForegroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.white;
            }
            return Colors.black;
          }),
          rangeSelectionBackgroundColor:
            accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
          cancelButtonStyle: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(
                Colors.black), // Set text color to black
            overlayColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
          ),
          confirmButtonStyle: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.black),
            backgroundColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
            overlayColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
          )),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: Colors.white,
        filled: true,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 15,
          color: Color(0xff489B79),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Color(0xff489B79),
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Colors.red,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Color(0xff6C6C6C),
            width: 0.3,
          ),
        ),
      ),
    );
  }

  void _listenAuthState(String accessType) async{
    await FirebaseAuth.instance.authStateChanges().listen((firebaseUser) async {
      final userProvider = context.read<UserProvider>();

      if (firebaseUser == null) {
        userProvider.clear();
        userProvider.setLoading(false);
        return;
      }

      final dynamic user = await getUser(firebaseUser.uid);

      if (user != null) {
        userProvider.setUser(user);
      } else {
        userProvider.clear();
      }
      userProvider.setLoading(false);
      userProvider.setAccessType(accessType);
    });
  }

  Future<NewarcUser> _getNewarcUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    NewarcUser newarcUser = NewarcUser.fromDocument(firestoreUser!, uid);

    return newarcUser;
  }

  Future<dynamic> getUser(String uid) async {
    if (uid == '') return null;

    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc = await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();


    if (firestoreUser != null) {
      if (firestoreUser['type'] == 'agency') {
        return await _getAgencyUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'newarc') {
        return await _getNewarcUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'professionals') {
        return await _getProfessionalsUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'supplier') {
        return await _getSupplierUser(firestoreUser, uid);
      } else if (firestoreUser['type'] == 'master') {
        if (firestoreUser['type'] == 'work') {
          return await _getNewarcUser(firestoreUser, uid);
        } else {
          return await _getAgencyUser(firestoreUser, uid);
        }
      }
    }
  }

  Future<ProfessionalsUser> _getProfessionalsUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreProfessionalsDoc =
    await fetchDocument('${appConfig.COLLECT_PROFESSIONALS}/${firestoreUser!["professionalId"]}');
    Map<String, dynamic>? firestoreProfessionals = firestoreProfessionalsDoc.data();

    ProfessionalsUser professionalsUser = ProfessionalsUser(firestoreUser, uid, firestoreProfessionals!, firestoreProfessionalsDoc.id);

    return professionalsUser;
  }


  Future<SupplierUser> _getSupplierUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreSupplierDoc =
    await fetchDocument('${appConfig.COLLECT_SUPPLIERS}/${firestoreUser!["supplierId"]}');
    Map<String, dynamic>? firestoreSupplier = firestoreSupplierDoc.data();

    SupplierUser supplierUser =
    SupplierUser(firestoreUser, uid, firestoreSupplier!, firestoreSupplierDoc.id);

    return supplierUser;
  }

  Future<AgencyUser> _getAgencyUser(
      Map<String, dynamic>? firestoreUser, String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreAgencyDoc =
    await fetchDocument('${appConfig.COLLECT_AGENCIES}/${firestoreUser!["agencyId"]}');
    Map<String, dynamic>? firestoreAgency = firestoreAgencyDoc.data();

    AgencyUser agencyUser =
    AgencyUser(firestoreUser, uid, firestoreAgency!, firestoreAgencyDoc.id);

    return agencyUser;
  }
}

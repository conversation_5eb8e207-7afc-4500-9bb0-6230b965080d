import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/home_newarc.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class CustomDrawer extends StatefulWidget {
  CustomDrawer(
      {Key? key,
      required this.newarcUser,
      })
      : super(key: key);

  final NewarcUser newarcUser;


  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer>
    with TickerProviderStateMixin {
  ReceivedContactsPageFilters? currentFilter;
  bool immaginaExpanded = false;
  ExpansibleController immaginaTile = new ExpansibleController();
  bool contattiTileExpand = false;
  ExpansibleController contattiTileKey = new ExpansibleController();
  bool progettiTileExpand = false;
  ExpansibleController progettiTileKey = new ExpansibleController();
  bool adsManagerTileExpand = false;
  ExpansibleController adsManagerTileKey = new ExpansibleController();
  bool ricercaTileExpand = false;
  ExpansibleController ricercaTileKey = new ExpansibleController();
  bool materiotecaTileExpand = false;
  ExpansibleController materiotecaTile = new ExpansibleController();
  bool gestioneTileExpand = false;
  ExpansibleController gestioneTile = new ExpansibleController();
  ExpansibleController restorationTile = new ExpansibleController();
  bool restorationTileExpand = false;
  ExpansibleController leadTile = new ExpansibleController();
  bool leadTileExpand = false;
  ExpansibleController fornitureTile = new ExpansibleController();
  bool fornitureTileExpand = false;
  bool isDrawerOpen = true;
  String selectedView = "";


  Map<String, dynamic> menuControls = {};

  @override
  void initState() {
    super.initState();

    if( widget.newarcUser.menuAccess == null ) {
      menuControls = getDefaultMenuAccessByRole(widget.newarcUser.role);
    } else {
      menuControls = widget.newarcUser.menuAccess as Map<String, dynamic>;
      // checking every menuAccess key is provided in the menuControls,
      // if not assign default value and update current user menuAccess
      Map<String, dynamic> defaultMenuAccess = getDefaultMenuAccessByRole(widget.newarcUser.role);
      for (var key in defaultMenuAccess.keys) {
        if (!menuControls.containsKey(key)) {
          menuControls[key] = defaultMenuAccess[key];
        }
      }
      widget.newarcUser.menuAccess = menuControls;
    }
  }

  void toggleDrawer() {
    setState(() {
      isDrawerOpen = !isDrawerOpen;
    });
  }

  Widget menuProgetti() {

    if( menuControls['progetti']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('progetti'),
        controller: progettiTileKey,
        initiallyExpanded: progettiTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            progettiTileExpand = value;
          });
        },
        minTileHeight: 0,
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Operazioni Newarc',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          progettiTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMenuItem('Progetti in corso', 'progetti-in-corso', menuControls['progetti'] ),
              _buildMenuItem('Storico progetti', 'storico-progetti', menuControls['progetti'] ),
              _buildMenuItem('C.E. Previsionali', 'c-e-p', menuControls['progetti'] ),
            ],
          ),
        ],
      ),
    );

  }
  
  Widget menuRistrutturazioni(){
    
    if( menuControls['ristrutturazioni']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('ristrutturazioni'),
        controller: restorationTile,
        initiallyExpanded: restorationTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            restorationTileExpand = value;
          });
        },
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Ristrutturazioni',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          restorationTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMenuItem('Segnalazioni', 'segnalazioni-ristrutturazione', menuControls['ristrutturazioni'] ),
              // _buildMenuItem('Clienti', 'contatti-ristrutturazione', menuControls['ristrutturazioni'] ),
              _buildMenuItem('Preventivi', 'renovation-quotation', menuControls['ristrutturazioni'] ),
              _buildMenuItem('Progetti', 'renovation-project', menuControls['ristrutturazioni'] ),
            ],
          ),
        ],
      ),
    );

  }

  Widget menuLeads(){
    
    // if( menuControls['ristrutturazioni']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('main-leads'),
        controller: leadTile,
        initiallyExpanded: leadTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            leadTileExpand = value;
          });
        },
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Gestisci Contatti',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          restorationTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMenuItem('Contatti', 'leads', menuControls['main-leads'] ),
              _buildMenuItem('Richieste visita', 'web-lead', menuControls['contatti']),
            ],
          ),
        ],
      ),
    );

  }

  Widget menuContatti() {
    
    if( menuControls['contatti']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('contatti'),
        controller: contattiTileKey,
        initiallyExpanded: contattiTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            contattiTileExpand = value;
          });
        },
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Contatti',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          contattiTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // _buildMenuItem('Valutazioni online', 'contatti-ricevuti', menuControls['contatti']),
              // _buildMenuItem('Lead acquisto', 'web-lead', menuControls['contatti']),
              // _buildMenuItem('Lead configuratore', 'newarc-lead', menuControls['contatti']),
              // _buildMenuItem('Registrazioni sito', 'web-client', menuControls['contatti']),
            ],
          ),
        ],
      ),
    );

  }

  Widget menuGestione() {

    if( menuControls['gestione']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('gestione'),
        controller: gestioneTile,
        initiallyExpanded: gestioneTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            gestioneTileExpand = value;
          });
        },
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Gestione',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          gestioneTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: Colors.white,
        ),
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildMenuItem('Agenzie', 'agencies', menuControls['gestione']),
              _buildMenuItem('Ditte', 'contractors', menuControls['gestione']),
              _buildMenuItem('Persone', 'team', menuControls['gestione']),
              _buildMenuItem('Professionals', 'professionals', menuControls['gestione']),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget menuAdManager(){

    if( menuControls['ads-manager']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('ads-manager'),
        controller: adsManagerTileKey,
        initiallyExpanded: adsManagerTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            adsManagerTileExpand = value;
          });
        },
        minTileHeight: 0,
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Ads Manager',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          adsManagerTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildMenuItem('Annunci attivi', 'active-ads', menuControls['ads-manager']),
              _buildMenuItem('Annunci archiviati', 'archived-ads', menuControls['ads-manager']),
            ],
          ),
        ],
      ),
    );

  }

  Widget menuImmagina() {

    if( menuControls['immagina']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('immagina'),
        controller: immaginaTile,
        initiallyExpanded: immaginaExpanded,
        onExpansionChanged: (value) {
          setState(() {
            immaginaExpanded = value;
          });
        },
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        minTileHeight: 0,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Newarc Immagina',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          immaginaExpanded
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildMenuItem('Richieste', 'richieste', menuControls['immagina']),
              _buildMenuItem('Progetti attivi', 'progetti-attivi', menuControls['immagina']),
              _buildMenuItem('Progetti archiviati', 'progetti-archiviati', menuControls['immagina']),
              
            ],
          ),
        ],
      ),
    );
  }

  Widget menuForniture() {
    
    if( menuControls['forniture']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('forniture'),
        controller: fornitureTile,
        initiallyExpanded: fornitureTileExpand,
        onExpansionChanged: (value) {
          setState(() {
            fornitureTileExpand = value;
          });
        },
        minTileHeight: 0,
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Forniture',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          fornitureTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMenuItem('Pavimenti', 'pavimenti', menuControls['forniture']),
              _buildMenuItem('Rivestimenti', 'rivestimenti', menuControls['forniture']),
              _buildMenuItem('Tinte', 'tinte', menuControls['forniture']),
              _buildMenuItem('Porte interne', 'porte-interne', menuControls['forniture']),
              _buildMenuItem('Sanitari', 'sanitari', menuControls['forniture']),
              _buildMenuItem('Illuminazione', 'illuminazione', menuControls['forniture']),
            ],
          ),
        ],
      ),
    );

  }

  Widget menuMaterioteca() {

    if( menuControls['materioteca']['status'] == 'hide' ) return Container();

    return Theme(
      data: Theme.of(context).copyWith(
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        key: const Key('materioteca'),
        controller: materiotecaTile,
        initiallyExpanded: materiotecaTileExpand,
        onExpansionChanged: (value) => setState(() {
          materiotecaTileExpand = value;
        }),
        minTileHeight: 0,
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        tilePadding: const EdgeInsets.only(left: 15, right: 15, top: 10),
        childrenPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: const Text(
          'Materioteca',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'semi-bold',
            color: Colors.white,
          ),
        ),
        trailing: SvgPicture.asset(
          materiotecaTileExpand
              ? 'assets/icons/arrow_up.svg'
              : 'assets/icons/arrow_down.svg',
          height: 6,
          width: 10,
          color: AppColor.greyColor,
        ),
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildMenuItem('Moodboard', 'moodboard', menuControls['materioteca']),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildMenuItem(String label, String viewKey, Map<String, dynamic> _menuControl) {
    
    if( _menuControl['children'][viewKey] == 'hide' ) return Container();

    return InkWell(
      
      onTap: () {
       selectedView = viewKey;
       setState(() {});
       handleMenuTap(viewKey);
      },
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 8),
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Raleway-400',
                color: getColor(viewKey),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void handleMenuTap(String viewKey) {
    switch (viewKey) {
      case 'progetti-in-corso':
        context.go(WorkRoutes.workProgettiInCorso);
        break;

      case 'storico-progetti':
        context.go(WorkRoutes.workStoricoProgetti);
        break;

      case 'c-e-p':
        context.go(WorkRoutes.workCEprevisionali);
        break;

      case 'segnalazioni-ristrutturazione':
        context.go(WorkRoutes.workSegnalazioni);
        break;

      case 'contatti-ristrutturazione':
        context.go(WorkRoutes.workClienti);
        break;

      case 'renovation-quotation':
        context.go(WorkRoutes.workPreventivi);
        break;

      case 'renovation-project':
        context.go(WorkRoutes.workRenovationProject);
        break;

      case 'leads':
        context.go(WorkRoutes.workGestisciContatti);
        break;

      case 'contatti-ricevuti':
        context.go(WorkRoutes.workValutazioniOnline);
        break;

      case 'web-lead':
        context.go(WorkRoutes.workLeadAcquisto);
        break;

      case 'newarc-lead':
        context.go(WorkRoutes.workLeadConfiguratore);
        break;

      case 'web-client':
        context.go(WorkRoutes.workRegistrazioniSito);
        break;

      case 'agencies':
        context.go(WorkRoutes.workAgenzie);
        break;

      case 'contractors':
        context.go(WorkRoutes.workDitte);
        break;

      case 'team':
        context.go(WorkRoutes.workPersone);
        break;

      case 'professionals':
        context.go(WorkRoutes.workProfessionals);
        break;

      case 'richieste':
        context.go(WorkRoutes.workImmagina("richieste"));
        break;

      case 'progetti-attivi':
        context.go(WorkRoutes.workImmagina("progetti-attivi"));
        break;

      case 'progetti-archiviati':
        context.go(WorkRoutes.workImmagina('progetti-archiviati'));
        break;

      case 'pavimenti':
        context.go(WorkRoutes.workPavimenti);
        break;

      case 'rivestimenti':
        context.go(WorkRoutes.workRivestimenti);
        break;

      case 'tinte':
        context.go(WorkRoutes.workTinte);
        break;

      case 'porte-interne':
        context.go(WorkRoutes.workPorteInterne);
        break;

      case 'sanitari':
        // context.go('/forniture/sanitari');
        break;

      case 'illuminazione':
        // context.go('/forniture/illuminazione');
        break;

      case 'moodboard':
        // context.go('/materioteca/moodboard');
        break;

      case 'active-ads':
        context.go(WorkRoutes.workAnnunciAttivi);
        break;

      case 'archived-ads':
        context.go(WorkRoutes.workAnnunciArchiviati);
        break;

      default:
        debugPrint('Unknown viewKey: $viewKey');
    }
  }


  @override
  Widget build(BuildContext context) {  
    return Theme(
      data: ThemeData(
        splashFactory: NoSplash.splashFactory,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: isDrawerOpen ? 257 : 80,
            color: Theme.of(context).primaryColorDark,
            child: Container(
              child: !isDrawerOpen
                  ? Column(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding:
                                EdgeInsets.only(left: 10, right: 20, top: 42),
                            child: Image.asset(
                              "assets/logo_work_white.png",
                              height: 25,
                            ),
                          ),
                        ),
                      ],
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ListView(
                            shrinkWrap: true,
                            padding: EdgeInsets.all(0),
                            children: [
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      left: 20, right: 20, top: 30),
                                  child: Image.asset(
                                    "assets/logo.png",
                                    height: 50,
                                  ),
                                ),
                              ),
                              SizedBox(height: 40),

                              // Menu items goes here
                              // menuContatti(),
                              menuImmagina(),
                              menuRistrutturazioni(),
                              menuLeads(),
                              menuAdManager(),
                              menuProgetti(),
                              menuForniture(),
                              menuGestione(),

                              // menuMaterioteca(),


                              appConfig.isProduction
                                  ? Container()
                                  : TextButton(
                                      onPressed: () {
                                        selectedView = 'widget';
                                        // widget.updateViewCallback(
                                        //     widget.selectedView);
                                      },
                                      child: Text(
                                        "Common Helpful Widget",
                                      ),
                                    ),
                            ],
                          ),
                        ),
                        (widget.newarcUser.role == null ||
                                (widget.newarcUser.role != 'master' &&
                                    widget.newarcUser.role != 'administration'))
                            ? Container()
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                mainAxisSize: MainAxisSize.max,
                                children: [

                                ],
                              ),
                      ],
                    ),
            ),
          ),
          Positioned(
            right: 5,
            top: 42,
            child: InkWell(
              onTap: toggleDrawer,
              child: Container(
                height: 28,
                width: 28,
                child: Center(
                  child: SvgPicture.asset(
                    isDrawerOpen
                        ? "assets/icons/arrow_left.svg"
                        : "assets/icons/arrow_right.svg",
                    color: AppColor.drawerIconButtonColor,
                    height: 10,
                  ),
                ),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                        offset: Offset(0, 10),
                        blurRadius: 10,
                        color: AppColor.black.withOpacity(0.1))
                  ],
                  color: AppColor.drawerButtonColor,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Color? getColor(String view) {
    List<String> breadcrumb = view.split('/');
    if (breadcrumb.indexOf(selectedView) >= 0) {
      return Theme.of(context).primaryColor;
    } else {
      return Colors.white;
    }
  }
}

enum ReceivedContactsPageFilters {
  vendiNewarc,
  vendiAgenzie,
  valutaCompra,
  valutaCuriosita,
  vendiProfessionista,
  valutaProfessionista
}
